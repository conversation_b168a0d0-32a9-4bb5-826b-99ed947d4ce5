package com.ray.landadmin.module.common.domain;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 *
 * 分页参数
 *
 * <AUTHOR>
 * 创建日期： 2025-08-15
 */
@Data
public class PageDto {

    /**
     * 当前页
     */
    @NotNull(message = "分页参数不能为空")
    @Min(value = 1, message = "分页参数最小值为1")
    private Integer page;

    /**
     * 每页数据量
     */
    @NotNull(message = "每页数量不能为空")
    @Max(value = 500, message = "每页最大为100")
    @Min(value = 1, message = "每页最小为1")
    private Integer limit;
}
