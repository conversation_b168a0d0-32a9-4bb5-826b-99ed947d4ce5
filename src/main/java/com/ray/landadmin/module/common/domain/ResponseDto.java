package com.ray.landadmin.module.common.domain;

import lombok.Data;

/**
 *
 * 响应数据
 *
 * <AUTHOR>
 * 创建日期： 2025-08-15
 */
@Data
public class ResponseDto<T> {
    /**
     * 状态码
     */
    private Integer code;

    /**
     * 状态信息
     */
    private String msg;

    /**
     * 数据
     */
    private T data;

    public static <T> ResponseDto<T> success(T data) {
        ResponseDto<T> responseDto = new ResponseDto<>();
        responseDto.setCode(200);
        responseDto.setMsg("success");
        responseDto.setData(data);
        return responseDto;
    }

}
