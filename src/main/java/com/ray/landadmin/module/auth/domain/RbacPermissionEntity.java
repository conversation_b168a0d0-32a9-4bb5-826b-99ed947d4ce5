package com.ray.landadmin.module.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * RBAC权限实体类
 *
 * <AUTHOR>
 * @since 2025-08-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("rbac_permission")
public class RbacPermissionEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = -1708102902408688213L;

    /**
     * 权限ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 权限名称
     */
    @TableField("name")
    private String name;

    /**
     * 权限标签
     */
    @TableField("label")
    private String label;

    /**
     * 权限描述
     */
    @TableField("description")
    private String description;

    /**
     * 权限规则
     */
    @TableField("rule")
    private String rule;
}
