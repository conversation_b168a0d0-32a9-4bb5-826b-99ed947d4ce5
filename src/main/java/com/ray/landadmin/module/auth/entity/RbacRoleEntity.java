package com.ray.landadmin.module.auth.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * RBAC角色实体类
 * 
 * <AUTHOR>
 * @since 2025-08-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("rbac_role")
public class RbacRoleEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 3821450426628294642L;

    /**
     * 角色ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 角色名称
     */
    @TableField("name")
    private String name;

    /**
     * 角色标签
     */
    @TableField("label")
    private String label;

    /**
     * 角色描述
     */
    @TableField("description")
    private String description;

    /**
     * 是否系统角色 (0-否, 1-是)
     */
    @TableField("systemed")
    private Integer systemed;

    /**
     * 删除时间（软删除）
     */
    @TableField("deletedAt")
    @TableLogic
    private LocalDateTime deletedAt;
}
