package com.ray.landadmin.module.user.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户实体类
 *
 * <AUTHOR>
 * @since 2025-08-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user")
public class UserEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = -7264197021663052934L;

    /**
     * 用户ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 昵称
     */
    @TableField("nickname")
    private String nickname;

    /**
     * 用户名
     */
    @TableField("username")
    private String username;

    /**
     * 密码
     */
    @TableField("password")
    private String password;

    /**
     * 手机号
     */
    @TableField("phone")
    private String phone;

    /**
     * 邮箱
     */
    @TableField("email")
    private String email;

    /**
     * 创建时间
     */
    @TableField("createdAt")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField("updatedAt")
    private LocalDateTime updatedAt;

    /**
     * 删除时间（软删除）
     */
    @TableField("deletedAt")
    @TableLogic
    private LocalDateTime deletedAt;

    /**
     * 是否激活 (0-未激活, 1-已激活)
     */
    @TableField("actived")
    private Integer actived;
}
