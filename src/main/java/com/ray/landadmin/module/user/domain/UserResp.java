package com.ray.landadmin.module.user.domain;

import lombok.Data;

import java.time.LocalDateTime;

/**
 *
 *
 * <AUTHOR>
 * 创建日期： 2025-08-15
 */
@Data
public class UserResp {
    /**
     * 用户ID
     */
    private String id;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 用户名
     */
    private String username;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 是否激活 (0-未激活, 1-已激活)
     */
    private Integer actived;
}
