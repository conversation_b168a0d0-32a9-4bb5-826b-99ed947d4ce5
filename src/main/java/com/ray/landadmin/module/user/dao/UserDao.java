package com.ray.landadmin.module.user.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ray.landadmin.module.user.domain.UserEntity;
import com.ray.landadmin.module.user.domain.UserReq;
import com.ray.landadmin.module.user.domain.UserResp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 *
 * <AUTHOR>
 * 创建日期： 2025-08-15
 */
@Mapper
public interface UserDao extends BaseMapper<UserEntity> {
    List<UserResp> selectUser(Page<UserResp> page, @Param("params") UserReq req);
}
