package com.ray.landadmin.module.user.service;

import com.ray.landadmin.module.user.dao.UserDao;
import com.ray.landadmin.module.user.entity.UserEntity;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *
 *
 * <AUTHOR>
 * 创建日期： 2025-08-15
 */
@Service
public class UserService {

    @Resource
    private UserDao userDao;
    public List<UserEntity> selectUser()
    {
        return userDao.selectUser();
    }
}
