package com.ray.landadmin.module.user.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ray.landadmin.module.common.domain.PageResp;
import com.ray.landadmin.module.common.domain.ResponseDto;
import com.ray.landadmin.module.user.dao.UserDao;
import com.ray.landadmin.module.user.domain.UserReq;
import com.ray.landadmin.module.user.domain.UserResp;
import com.ray.landadmin.util.PageUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *
 *
 * <AUTHOR>
 * 创建日期： 2025-08-15
 */
@Service
public class UserService {

    @Resource
    private UserDao userDao;

    public ResponseDto<PageResp<UserResp>> selectUser(UserReq req) {
        Page<UserResp> page = PageUtil.convert2PageQuery(req);
        List<UserResp> userRespList = userDao.selectUser(page, req);
        PageResp<UserResp> pageResp = PageUtil.convert2PageResult(page, userRespList);
        return ResponseDto.success(pageResp);
    }
}
