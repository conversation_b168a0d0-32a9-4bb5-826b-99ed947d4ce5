package com.ray.landadmin.module.user.controller;

import com.ray.landadmin.module.common.domain.PageResp;
import com.ray.landadmin.module.common.domain.ResponseDto;
import com.ray.landadmin.module.user.domain.UserReq;
import com.ray.landadmin.module.user.domain.UserResp;
import com.ray.landadmin.module.user.service.UserService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 *
 * <AUTHOR>
 * 创建日期： 2025-08-15
 */
@RestController
public class UserController {

    @Resource
    private UserService userService;

    @GetMapping("/users")
    public ResponseDto<PageResp<UserResp>> selectUser(@Valid UserReq req) {
        return userService.selectUser(req);
    }
}
