package com.ray.landadmin.module.user.controller;

import com.ray.landadmin.module.user.entity.UserEntity;
import com.ray.landadmin.module.user.service.UserService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 *
 *
 * <AUTHOR>
 * 创建日期： 2025-08-15
 */
@RestController
public class UserController {

    @Resource
    private UserService userService;

    @GetMapping("/users")
    public List<UserEntity> selectUser()
    {
        return userService.selectUser();
    }
}
