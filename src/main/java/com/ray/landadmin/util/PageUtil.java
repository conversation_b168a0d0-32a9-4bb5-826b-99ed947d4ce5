package com.ray.landadmin.util;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ray.landadmin.module.common.domain.PageDto;
import com.ray.landadmin.module.common.domain.PageResp;
import com.ray.landadmin.module.common.domain.PaginateMeta;
import com.ray.landadmin.module.user.domain.UserResp;

import java.util.List;

/**
 *
 *
 * <AUTHOR>
 * 创建日期： 2025-08-15
 */
public class PageUtil {
    public static <T> Page<T> convert2PageQuery(PageDto pageDto) {
        return Page.of(pageDto.getPage(), pageDto.getLimit());
    }

    public static <T> PageResp<T> convert2PageResult(Page<T> page, List<T> datas) {
        PageResp<T> pageResp= new PageResp<>();
        PaginateMeta meta = new PaginateMeta();
        meta.setItemCount(null == datas ? 0 : datas.size());
        meta.setTotalItems(page.getTotal());
        meta.setPerPage(page.getSize());
        meta.setTotalPages(page.getPages());
        meta.setCurrentPage(page.getCurrent());
        pageResp.setMeta(meta);
        pageResp.setItems(datas);
        return pageResp;
    }
}
