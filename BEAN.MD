## javabean 命名规范
### javabean 的整体要求：

不得有任何的业务逻辑或者计算
基本数据类型必须使用包装类型（Integer, Double、Boolean 等）
不允许有任何的默认值
每个属性必须添加注释，并且必须使用多行注释。
必须使用 lombok 简化 getter/setter 方法
建议对象使用 lombok 的 @Builder ，@NoArgsConstructor，同时使用这两个注解，简化对象构造方法以及 set 方法。
### javabean 名字划分：

XxxEntity 数据库持久对象
XxxResp 返回前端对象 
XxxReq 前端请求对象
XxxDTO 数据传输对象
XxxBO 内部处理对象

### 数据对象；XxxxEntity，要求：

以 Entity 为结尾
Xxxx 与数据库表名保持一致
类中字段要与数据库字段保持一致，不能缺失或者多余
类中的每个字段添加注释，并与数据库注释保持一致
不允许有组合
项目内的日期类型必须统一，使用 java.time.LocalDateTime 或者 java.time.LocalDate

### 请求对象；XxxxReq，要求：

不可以继承自 Entity
Req 可以继承、组合其他 DTO，Resp，BO 等对象
Req 只能用于前端、RPC 的请求参数

### 返回对象；XxxxResp，要求：

不可继承自 Entity
Resp 可以继承、组合其他 DTO，Resp，BO 等对象
Resp 只能用于返回前端、rpc 的业务数据封装对象

### 业务对象 BO，要求：

不可以继承自 Entity
BO 对象只能用于 service，manager，dao 层，不得用于 controller 层